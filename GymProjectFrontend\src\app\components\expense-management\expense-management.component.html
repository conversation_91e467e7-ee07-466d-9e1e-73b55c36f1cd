<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="spinner-container">
      <app-loading-spinner></app-loading-spinner>
    </div>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <!-- Summary Cards Row (Günlük, Aylık, Yıllık) -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Daily Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card daily-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-day"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalDailyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">Bugünkü Toplam Gider</p>
              <div class="trend-indicator" *ngIf="getDailyTrend() as trend">
                <i class="fas" [ngClass]="trend.icon" [style.color]="trend.color"></i>
                <span class="trend-text" [style.color]="trend.color">{{ trend.text }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- Monthly Total Expense Card -->
        <div class="col-md-4 mb-3">
          <div class="modern-stats-card monthly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalMonthlyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">{{ getMonthName(selectedMonth) }} {{ selectedYear }} Toplam Gider</p>
              <div class="trend-indicator" *ngIf="getMonthlyTrend() as trend">
                <i class="fas" [ngClass]="trend.icon" [style.color]="trend.color"></i>
                <span class="trend-text" [style.color]="trend.color">{{ trend.text }}</span>
              </div>
            </div>
          </div>
        </div>
         <!-- Yearly Total Expense Card -->
         <div class="col-md-4 mb-3">
          <div class="modern-stats-card yearly-expense-card">
            <div class="modern-stats-icon">
              <i class="fas fa-calendar-week"></i>
            </div>
            <div class="modern-stats-info">
              <h3 class="modern-stats-value">{{ totalYearlyExpense | currency:'₺':'symbol':'1.2-2':'tr' }}</h3>
              <p class="modern-stats-label">{{ selectedYear }} Yılı Toplam Gider</p>
              <div class="trend-indicator" *ngIf="getYearlyTrend() as trend">
                <i class="fas" [ngClass]="trend.icon" [style.color]="trend.color"></i>
                <span class="trend-text" [style.color]="trend.color">{{ trend.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="col-md-12 mb-4">
      <div class="row">
        <!-- Expense Distribution Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Gider Dağılımı (Türe Göre)</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="expenseDistributionChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="col-md-6 mb-4">
          <div class="modern-card h-100">
            <div class="modern-card-header">
              <h5 class="mb-0">Aylık Gider Trendi</h5>
            </div>
            <div class="modern-card-body">
              <div class="chart-container" style="position: relative; height: 300px;">
                <canvas id="monthlyTrendChart"></canvas>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content: Table and Filters -->
    <div class="col-md-12">
      <div class="modern-card">
        <!-- Card Header: Title, Actions -->
        <div class="modern-card-header">
          <h5 class="mb-0">Gider Kayıtları</h5>
          <div class="d-flex gap-2 align-items-center flex-wrap">
            <!-- Export Buttons -->
            <button class="modern-btn modern-btn-outline-primary modern-btn-sm"
                    (click)="exportToExcel()"
                    [disabled]="isExporting"
                    title="Excel'e Aktar">
              <i class="fas fa-spinner fa-spin me-1" *ngIf="isExporting"></i>
              <fa-icon [icon]="faFileExcel" class="modern-btn-icon" *ngIf="!isExporting"></fa-icon>
              {{ isExporting ? 'Hazırlanıyor...' : 'Excel' }}
            </button>
            <!-- Add Expense Button -->
            <button class="modern-btn modern-btn-primary modern-btn-sm" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> Yeni Gider Ekle
            </button>
          </div>
        </div>

        <!-- Advanced Filters -->
        <div class="modern-card-body border-bottom">
          <div class="row g-3">
            <!-- Search Filter -->
            <div class="col-md-3">
              <div class="search-input-container">
                <i class="fas fa-search search-icon" *ngIf="!isSearching"></i>
                <i class="fas fa-spinner fa-spin search-icon" *ngIf="isSearching"></i>
                <input
                  type="text"
                  class="search-input modern-form-control modern-form-control-sm"
                  placeholder="Tür veya açıklama ara..."
                  [formControl]="searchControl" />
              </div>
            </div>

            <!-- Date Range Filter -->
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Başlangıç Tarihi"
                [(ngModel)]="filterState.startDate"
                (ngModelChange)="onAdvancedFilterChange()" />
            </div>
            <div class="col-md-2">
              <input
                type="date"
                class="modern-form-control modern-form-control-sm"
                placeholder="Bitiş Tarihi"
                [(ngModel)]="filterState.endDate"
                (ngModelChange)="onAdvancedFilterChange()" />
            </div>

            <!-- Expense Type Filter -->
            <div class="col-md-2">
              <select
                class="modern-form-control modern-form-control-sm"
                [(ngModel)]="filterState.expenseType"
                (ngModelChange)="onAdvancedFilterChange()">
                <option value="">Tüm Türler</option>
                <option *ngFor="let type of expenseTypes" [value]="type">{{ type }}</option>
              </select>
            </div>

            <!-- Amount Range Filter -->
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Min ₺"
                [(ngModel)]="filterState.minAmount"
                (ngModelChange)="onAdvancedFilterChange()" />
            </div>
            <div class="col-md-1">
              <input
                type="number"
                class="modern-form-control modern-form-control-sm"
                placeholder="Max ₺"
                [(ngModel)]="filterState.maxAmount"
                (ngModelChange)="onAdvancedFilterChange()" />
            </div>

            <!-- Filter Actions -->
            <div class="col-md-1">
              <div class="d-flex gap-1">
                <button class="modern-btn modern-btn-secondary modern-btn-sm"
                        (click)="clearFilters()"
                        [disabled]="!hasActiveFilters()"
                        title="Filtreleri Temizle">
                  <i class="fas fa-filter-circle-xmark"></i>
                </button>
                <button class="modern-btn modern-btn-info modern-btn-sm"
                        (click)="saveFavoriteFilter()"
                        [disabled]="!hasActiveFilters()"
                        title="Favori Olarak Kaydet">
                  <i class="fas fa-star"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Quick Filters -->
          <div class="row g-2 mt-2 pt-3 border-top">
            <div class="col-12">
              <label class="form-label small fw-bold">Hızlı Filtreler:</label>
              <div class="d-flex gap-2 flex-wrap">
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="applyQuickFilter('today')">
                  <i class="fas fa-calendar-day me-1"></i> Bugün
                </button>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="applyQuickFilter('thisWeek')">
                  <i class="fas fa-calendar-week me-1"></i> Bu Hafta
                </button>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="applyQuickFilter('thisMonth')">
                  <i class="fas fa-calendar-alt me-1"></i> Bu Ay
                </button>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="applyQuickFilter('lastMonth')">
                  <i class="fas fa-calendar-minus me-1"></i> Geçen Ay
                </button>
                <button class="modern-btn modern-btn-outline-primary modern-btn-sm" (click)="applyQuickFilter('thisYear')">
                  <i class="fas fa-calendar me-1"></i> Bu Yıl
                </button>
                <button class="modern-btn modern-btn-outline-secondary modern-btn-sm" (click)="toggleFavoriteFilters()">
                  <i class="fas fa-star me-1"></i> Favoriler
                  <i class="fas fa-chevron-down ms-1" [class.fa-chevron-up]="showFavoriteFilters"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Favorite Filters -->
          <div class="row g-2 mt-2" *ngIf="showFavoriteFilters">
            <div class="col-12">
              <div class="favorite-filters-container">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <label class="form-label small fw-bold mb-0">Favori Filtreler:</label>
                  <small class="text-muted">{{ favoriteFilters.length }} favori</small>
                </div>
                <div class="favorite-filters-list" *ngIf="favoriteFilters.length > 0; else noFavorites">
                  <div class="favorite-filter-item" *ngFor="let filter of favoriteFilters; let i = index">
                    <div class="favorite-filter-info">
                      <span class="favorite-filter-name">{{ filter.name }}</span>
                      <small class="favorite-filter-date">{{ filter.createdAt | date:'dd/MM/yyyy HH:mm' }}</small>
                    </div>
                    <div class="favorite-filter-actions">
                      <button class="modern-btn modern-btn-primary modern-btn-sm"
                              (click)="loadFavoriteFilter(filter)"
                              title="Filtreyi Uygula">
                        <i class="fas fa-play"></i>
                      </button>
                      <button class="modern-btn modern-btn-danger modern-btn-sm"
                              (click)="deleteFavoriteFilter(i)"
                              title="Filtreyi Sil">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
                <ng-template #noFavorites>
                  <div class="text-center text-muted py-3">
                    <i class="fas fa-star fa-2x mb-2"></i>
                    <p class="mb-0">Henüz favori filtre yok</p>
                    <small>Filtreleri ayarlayıp ⭐ butonuna tıklayarak kaydedin</small>
                  </div>
                </ng-template>
              </div>
            </div>
          </div>

          <!-- Dashboard Year/Month Filters -->
          <div class="row g-3 mt-2 pt-3 border-top">
            <div class="col-md-2">
              <label class="form-label small">Dashboard Yılı:</label>
              <select class="modern-form-control modern-form-control-sm" [(ngModel)]="selectedYear" (ngModelChange)="onFilterChange()">
                <option *ngFor="let year of years" [value]="year">{{ year }}</option>
              </select>
            </div>
            <div class="col-md-2">
              <label class="form-label small">Dashboard Ayı:</label>
              <select class="modern-form-control modern-form-control-sm" [(ngModel)]="selectedMonth" (ngModelChange)="onFilterChange()">
                <option *ngFor="let month of months" [value]="month.value">{{ month.name }}</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Card Body: Table and Pagination -->
        <div class="modern-card-body">
          <!-- Table Info and Page Size -->
          <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="table-info">
              <span class="text-muted">
                Toplam {{ paginatedExpenses.totalCount }} kayıttan
                {{ (paginatedExpenses.pageNumber - 1) * paginatedExpenses.pageSize + 1 }}-{{
                  Math.min(paginatedExpenses.pageNumber * paginatedExpenses.pageSize, paginatedExpenses.totalCount)
                }} arası gösteriliyor
              </span>
            </div>
            <div class="page-size-selector">
              <label class="form-label me-2 mb-0">Sayfa başına:</label>
              <select class="modern-form-control modern-form-control-sm"
                      [(ngModel)]="paginatedExpenses.pageSize"
                      (ngModelChange)="changePageSize($event)"
                      style="width: auto; display: inline-block;">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="50">50</option>
              </select>
            </div>
          </div>

          <!-- Expense Table -->
          <div class="table-responsive" *ngIf="!isLoading && paginatedExpenses.data.length > 0">
            <table class="modern-table">
              <thead>
                <tr>
                  <th class="text-center sortable" (click)="sortBy('ExpenseType')">
                    Tür
                    <fa-icon [icon]="getSortIcon('ExpenseType')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('Amount')">
                    Tutar
                    <fa-icon [icon]="getSortIcon('Amount')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('ExpenseDate')">
                    Gider Tarihi
                    <fa-icon [icon]="getSortIcon('ExpenseDate')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center sortable" (click)="sortBy('Description')">
                    Açıklama
                    <fa-icon [icon]="getSortIcon('Description')" class="ms-1"></fa-icon>
                  </th>
                  <th class="text-center">İşlemler</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let expense of paginatedExpenses.data; let i = index"
                    [style.animation-delay]="i * 0.05 + 's'"
                    class="staggered-item">
                  <td class="text-center" data-label="Tür">
                    <div class="expense-type-cell" *ngIf="expense.expenseType; else noType">
                      <div class="expense-type-icon" [title]="getExpenseTypeTooltip(expense.expenseType)">
                        <i [class]="getExpenseTypeIcon(expense.expenseType)"></i>
                      </div>
                      <span class="modern-badge" [ngClass]="getBadgeClass(expense.expenseType)">
                        {{ expense.expenseType }}
                      </span>
                    </div>
                    <ng-template #noType>
                      <div class="expense-type-cell">
                        <div class="expense-type-icon" title="Gider türü belirtilmemiş">
                          <i class="fas fa-question-circle text-muted"></i>
                        </div>
                        <span class="text-muted">-</span>
                      </div>
                    </ng-template>
                  </td>
                  <td class="text-center" data-label="Tutar">
                    <div class="amount-cell" [title]="'Tutar: ' + (expense.amount | currency:'₺':'symbol':'1.2-2':'tr')">
                      <i class="fas fa-lira-sign amount-icon"></i>
                      <span class="amount-value">{{ expense.amount | currency:'':'symbol':'1.2-2':'tr' }}</span>
                    </div>
                  </td>
                  <td class="text-center" data-label="Gider Tarihi">
                    <div class="date-cell" [title]="'Gider Tarihi: ' + (expense.expenseDate | date:'dd MMMM yyyy, EEEE':'tr')">
                      <i class="fas fa-calendar-alt date-icon"></i>
                      <span class="date-value">{{ expense.expenseDate | date:'dd/MM/yyyy' }}</span>
                    </div>
                  </td>
                  <td class="text-center" data-label="Açıklama">
                    <div class="description-cell" *ngIf="expense.description; else noDescription"
                         [title]="expense.description">
                      <i class="fas fa-comment-alt description-icon"></i>
                      <span class="description-value">{{
                        expense.description.length > 30 ?
                        (expense.description | slice:0:30) + '...' :
                        expense.description
                      }}</span>
                    </div>
                    <ng-template #noDescription>
                      <div class="description-cell">
                        <i class="fas fa-comment-slash text-muted description-icon"></i>
                        <span class="text-muted">Açıklama yok</span>
                      </div>
                    </ng-template>
                  </td>
                  <td data-label="İşlemler">
                    <div class="d-flex justify-content-center gap-2 action-buttons">
                      <button
                        class="modern-btn modern-btn-primary modern-btn-sm modern-btn-icon-only action-btn"
                        [title]="'Düzenle: ' + (expense.expenseType || 'Gider')"
                        (click)="openExpenseDialog(expense)"
                        [attr.aria-label]="'Gideri düzenle'">
                        <fa-icon [icon]="faEdit"></fa-icon>
                      </button>
                      <button
                        class="modern-btn modern-btn-danger modern-btn-sm modern-btn-icon-only action-btn"
                        [title]="'Sil: ' + (expense.expenseType || 'Gider') + ' - ' + (expense.amount | currency:'₺':'symbol':'1.2-2':'tr')"
                        (click)="deleteExpense(expense)"
                        [attr.aria-label]="'Gideri sil'">
                        <fa-icon [icon]="faTrashAlt"></fa-icon>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="pagination-container mt-4" *ngIf="!isLoading && paginatedExpenses.totalPages > 1">
            <nav aria-label="Sayfa navigasyonu">
              <ul class="pagination justify-content-center">
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasPrevious">
                  <button class="page-link" (click)="goToPage(1)" [disabled]="!paginatedExpenses.hasPrevious">
                    <i class="fas fa-angle-double-left"></i>
                  </button>
                </li>
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasPrevious">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.pageNumber - 1)" [disabled]="!paginatedExpenses.hasPrevious">
                    <i class="fas fa-angle-left"></i>
                  </button>
                </li>

                <li class="page-item"
                    *ngFor="let page of getPageNumbers()"
                    [class.active]="page === paginatedExpenses.pageNumber">
                  <button class="page-link" (click)="goToPage(page)">{{ page }}</button>
                </li>

                <li class="page-item" [class.disabled]="!paginatedExpenses.hasNext">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.pageNumber + 1)" [disabled]="!paginatedExpenses.hasNext">
                    <i class="fas fa-angle-right"></i>
                  </button>
                </li>
                <li class="page-item" [class.disabled]="!paginatedExpenses.hasNext">
                  <button class="page-link" (click)="goToPage(paginatedExpenses.totalPages)" [disabled]="!paginatedExpenses.hasNext">
                    <i class="fas fa-angle-double-right"></i>
                  </button>
                </li>
              </ul>
            </nav>
          </div>

          <!-- No Results Message (Empty State) -->
          <div class="empty-state" *ngIf="!isLoading && paginatedExpenses.data.length === 0">
            <i class="fas fa-ghost fa-3x text-muted mb-3"></i>
            <h5 class="mt-3">Gider Bulunamadı</h5>
            <p class="text-muted">Seçili kriterlere uygun gider kaydı bulunamadı.</p>
            <button class="modern-btn modern-btn-primary mt-3" (click)="openExpenseDialog()">
              <fa-icon [icon]="faPlus" class="modern-btn-icon"></fa-icon> İlk Gideri Ekle
            </button>
          </div>

        </div> <!-- End Card Body -->
      </div> <!-- End Modern Card -->
    </div> <!-- End Col -->
  </div> <!-- End Row -->
</div> <!-- End Container -->