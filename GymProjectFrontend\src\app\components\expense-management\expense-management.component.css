/* Loading Spinner Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* Use CSS variable for background with opacity */
  background-color: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px); /* Optional blur effect */
  transition: opacity 0.3s ease;
}

.spinner-container {
  text-align: center;
  animation: zoomIn 0.5s ease-out; /* Add animation */
}

/* Content Blur when Loading */
.content-blur {
  filter: blur(5px);
  transition: filter 0.3s ease;
}

/* Modern Stats Card Overrides/Specifics */
.modern-stats-card {
  /* Base styles come from modern-components.css */
  /* Add specific background/icon colors */
  transition: all var(--transition-speed) var(--transition-timing); /* Ensure transition */
}

.modern-stats-card:hover {
   transform: translateY(-5px);
   box-shadow: var(--shadow-md);
}

.modern-stats-icon {
   /* Base styles come from modern-components.css */
   color: var(--white); /* Ensure icon color is white */
   box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
   transition: transform var(--transition-speed) var(--transition-timing);
}

.modern-stats-card:hover .modern-stats-icon {
    transform: scale(1.1);
}

.modern-stats-value {
    /* Base styles come from modern-components.css */
    transition: color var(--transition-speed) var(--transition-timing);
}

/* Daily Expense Card (Example: Info color) */
.daily-expense-card {
  background: linear-gradient(135deg, var(--info-light) 0%, rgba(var(--info-rgb), 0.2) 100%);
}
.daily-expense-card .modern-stats-icon {
  background: linear-gradient(135deg, var(--info) 0%, #0b7b9a 100%); /* Example dark info */
}
.daily-expense-card .modern-stats-value {
  color: var(--info);
}

/* Monthly Expense Card (Example: Primary color) */
.monthly-expense-card {
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);
}
.monthly-expense-card .modern-stats-icon {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
}
.monthly-expense-card .modern-stats-value {
  color: var(--primary);
}

/* Yearly Expense Card (Example: Success color) */
.yearly-expense-card {
  background: linear-gradient(135deg, var(--success-light) 0%, rgba(var(--success-rgb), 0.2) 100%);
}
.yearly-expense-card .modern-stats-icon {
  background: linear-gradient(135deg, var(--success) 0%, #1a6e2f 100%); /* Example dark success */
}
.yearly-expense-card .modern-stats-value {
  color: var(--success);
}

/*  Eski kart stilleri yorum satırı içinde kalabilir veya tamamen silinebilir */
/*
.count-expense-card { ... }
.avg-expense-card { ... }
.total-expense-card { ... }
*/

/* Filter Icon Style */
.filter-icon {
  color: var(--text-secondary);
  font-size: 1rem; /* Stil tanımı eklendi */
}

/* Chart Container */
.chart-container {
  position: relative;
  margin: auto;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm); /* Reduced padding */
  background-color: rgba(var(--secondary-rgb), 0.05); /* Subtle background */
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.04);
  transition: all var(--transition-speed) var(--transition-timing);
  min-height: 300px; /* Ensure container has height */
  display: flex; /* Center empty state */
  align-items: center;
  justify-content: center;
}

.chart-container:hover {
  box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08);
}

.chart-empty-state {
  color: var(--text-secondary);
  font-style: italic;
  font-size: 0.9rem;
}


/* Filter Icon Style */
.filter-icon {
  color: var(--text-secondary);
  font-size: 1rem;
}

/* Active Filters */
.active-filters {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: var(--border-radius-md);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.filter-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);
  color: var(--primary);
  padding: 0.4rem 0.8rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.85rem;
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed) var(--transition-timing);
}

.filter-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.15);
}

.filter-badge .btn-close {
  font-size: 0.65rem;
  padding: 0.25rem;
  margin-left: 0.5rem;
  background-color: rgba(var(--primary-rgb), 0.2);
  border-radius: 50%;
  transition: all var(--transition-speed) var(--transition-timing);
  opacity: 0.7;
}

.filter-badge .btn-close:hover {
  background-color: rgba(var(--primary-rgb), 0.3);
  transform: rotate(90deg);
  opacity: 1;
}


/* Empty State Styles */
.empty-state {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  margin: var(--spacing-md) 0;
  transition: all var(--transition-speed) var(--transition-timing);
  border: 1px dashed var(--border-color);
}

.empty-state i,
.empty-state fa-icon {
  font-size: 3rem;
  color: var(--text-secondary);
  opacity: 0.6;
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.empty-state:hover i,
.empty-state:hover fa-icon {
  transform: scale(1.1) rotate(-5deg);
  opacity: 0.8;
}

.empty-state h5 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.empty-state p {
  color: var(--text-secondary);
  font-size: 1rem;
  max-width: 85%;
  margin: 0 auto var(--spacing-md);
}

.empty-state .modern-btn {
  transition: all var(--transition-speed) var(--transition-timing);
  padding: 0.6rem 1.5rem;
  font-weight: 500;
}

.empty-state .modern-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

/* Table Specifics */
.modern-table td .modern-badge {
  min-width: 80px; /* Ensure badges have some minimum width */
  text-align: center;
}

.modern-table td[style*="font-weight: 700"] {
   letter-spacing: 0.5px; /* Add slight spacing to amount */
}

/* Staggered animation for table rows */
.staggered-item {
  animation: fadeInTableRow 0.6s ease-out forwards;
  opacity: 0;
}

@keyframes fadeInTableRow {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure icon margins are consistent */
.modern-btn-icon {
    margin-right: 0.5rem;
}
.modern-btn-icon-only fa-icon,
.modern-btn-icon-only i {
    margin-right: 0; /* No margin for icon-only buttons */
}

/* Dark Mode Adjustments (if needed beyond modern-components.css) */
[data-theme="dark"] .chart-container {
   background-color: rgba(var(--secondary-rgb), 0.1); /* Darker subtle background */
   box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .active-filters {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-color: rgba(var(--primary-rgb), 0.2);
}

[data-theme="dark"] .filter-badge {
   background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.15) 0%, rgba(var(--primary-rgb), 0.25) 100%);
   color: var(--primary); /* Primary color should be adjusted for dark mode in variables */
}

[data-theme="dark"] .filter-badge .btn-close {
   background-color: rgba(var(--primary-rgb), 0.3);
}
[data-theme="dark"] .filter-badge .btn-close:hover {
   background-color: rgba(var(--primary-rgb), 0.4);
}

/* Search Input Styles (from member.component.css) */
/* Search Input (member.component.css'den alındı) */
.search-input-container {
  position: relative;
  /* margin-bottom: 1rem; */ /* Header içindeki diğer filtrelerle hizalama için kaldırıldı */
}

.search-icon {
  position: absolute;
  left: 1rem; /* member.component.css'deki değer */
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted); /* member.component.css'deki değer */
  /* z-index, font-size, line-height kaldırıldı, gerekirse eklenebilir */
}

.search-input {
  width: 100%; /* Kapsayıcı genişliğini doldurması için */
  padding: 0.75rem 1rem 0.75rem 2.5rem; /* member.component.css'deki padding (ikon için sol padding dahil) */
  border-radius: 0.5rem; /* member.component.css'deki değer */
  border: 1px solid var(--border-color); /* member.component.css'deki değer */
  background-color: var(--input-bg); /* member.component.css'deki değer */
  color: var(--input-text); /* member.component.css'deki değer */
  transition: all 0.3s ease; /* member.component.css'deki değer */
  /* height, line-height kaldırıldı, padding ile ayarlanıyor */
}

.search-input:focus {
  border-color: var(--primary-color); /* member.component.css'deki değer */
  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25); /* member.component.css'deki değer */
  outline: none;
}

/* Sortable Table Headers */
.sortable {
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-speed) var(--transition-timing);
  position: relative;
}

.sortable:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
}

.sortable fa-icon {
  opacity: 0.6;
  transition: opacity var(--transition-speed) var(--transition-timing);
}

.sortable:hover fa-icon {
  opacity: 1;
}

/* Pagination Styles */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-lg);
}

.pagination {
  margin: 0;
}

.page-item {
  margin: 0 2px;
}

.page-link {
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  padding: 0.5rem 0.75rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.page-link:hover:not(:disabled) {
  background-color: var(--primary-light);
  border-color: var(--primary);
  color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
  font-weight: 600;
}

.page-item.disabled .page-link {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
}

.page-item.disabled .page-link:hover {
  transform: none;
  box-shadow: none;
}

/* Table Info and Page Size Selector */
.table-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-size-selector .form-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
  white-space: nowrap;
}

/* Advanced Filters */
.modern-card-body.border-bottom {
  border-bottom: 1px solid var(--border-color) !important;
}

/* Dark Mode Pagination */
[data-theme="dark"] .page-link {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .page-link:hover:not(:disabled) {
  background-color: rgba(var(--primary-rgb), 0.2);
  border-color: var(--primary);
  color: var(--primary);
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--white);
}

[data-theme="dark"] .page-item.disabled .page-link {
  background-color: rgba(var(--bg-secondary-rgb), 0.5);
  color: var(--text-secondary);
}

/* Trend Indicators */
.trend-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
  font-size: 0.85rem;
  font-weight: 500;
}

.trend-indicator i {
  font-size: 0.9rem;
  animation: pulse 2s infinite;
}

.trend-text {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Enhanced Chart Containers */
.chart-container canvas {
  border-radius: var(--border-radius-md);
  transition: all var(--transition-speed) var(--transition-timing);
}

.chart-container:hover canvas {
  transform: scale(1.02);
  box-shadow: var(--shadow-lg);
}

/* Chart Loading State */
.chart-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: var(--text-secondary);
  font-style: italic;
}

.chart-loading i {
  margin-right: 0.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Modern Stats Cards */
.modern-stats-card {
  position: relative;
  overflow: hidden;
}

.modern-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.modern-stats-card:hover::before {
  transform: translateX(100%);
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
  .modern-card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .modern-card-header > div {
    justify-content: center;
  }
}

@media (max-width: 992px) {
  .expense-type-cell {
    flex-direction: column;
    gap: 0.25rem;
  }

  .amount-cell,
  .date-cell,
  .description-cell {
    flex-direction: column;
    gap: 0.25rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.5rem 0.25rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .trend-indicator {
    font-size: 0.75rem;
    gap: 0.25rem;
  }

  .trend-indicator i {
    font-size: 0.8rem;
  }

  .chart-container {
    min-height: 250px;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }

  .page-item {
    margin: 2px;
  }

  /* Mobile table improvements */
  .modern-table {
    font-size: 0.8rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.4rem 0.2rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn {
    padding: 0.25rem;
    min-width: 32px;
    height: 32px;
  }

  /* Mobile filters */
  .row.g-3 {
    --bs-gutter-x: 0.5rem;
  }

  .modern-form-control-sm {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }
}

@media (max-width: 576px) {
  .modern-stats-card {
    margin-bottom: 1rem;
  }

  .modern-stats-value {
    font-size: 1.5rem;
  }

  .modern-stats-label {
    font-size: 0.8rem;
  }

  /* Stack table on very small screens */
  .table-responsive {
    border: none;
  }

  .modern-table,
  .modern-table thead,
  .modern-table tbody,
  .modern-table th,
  .modern-table td,
  .modern-table tr {
    display: block;
  }

  .modern-table thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .modern-table tr {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
  }

  .modern-table td {
    border: none;
    position: relative;
    padding: 0.5rem 0;
    text-align: left !important;
  }

  .modern-table td:before {
    content: attr(data-label) ": ";
    font-weight: bold;
    color: var(--text-secondary);
    display: inline-block;
    width: 100px;
    margin-right: 0.5rem;
  }

  .expense-type-cell,
  .amount-cell,
  .date-cell,
  .description-cell {
    justify-content: flex-start;
  }

  .action-buttons {
    justify-content: flex-start;
    margin-top: 0.5rem;
  }

  /* Mobile pagination */
  .pagination {
    font-size: 0.8rem;
  }

  .page-link {
    padding: 0.4rem 0.6rem;
    min-width: 32px;
    height: 32px;
  }
}

/* Performance optimizations */
.modern-table tbody tr {
  will-change: transform;
}

.staggered-item {
  will-change: opacity, transform;
}

.chart-container canvas {
  will-change: transform;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .staggered-item,
  .modern-table tbody tr,
  .chart-container canvas,
  .action-btn,
  .modern-badge {
    animation: none !important;
    transition: none !important;
  }

  .trend-indicator i {
    animation: none !important;
  }
}

/* Favorite Filters Styles */
.favorite-filters-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: 1rem;
  margin-top: 0.5rem;
}

.favorite-filters-list {
  max-height: 200px;
  overflow-y: auto;
}

.favorite-filter-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-speed) var(--transition-timing);
}

.favorite-filter-item:hover {
  background: rgba(var(--primary-rgb), 0.05);
  border-color: var(--primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.favorite-filter-item:last-child {
  margin-bottom: 0;
}

.favorite-filter-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.favorite-filter-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.favorite-filter-date {
  color: var(--text-secondary);
  font-size: 0.75rem;
}

.favorite-filter-actions {
  display: flex;
  gap: 0.5rem;
}

.favorite-filter-actions .modern-btn {
  padding: 0.25rem 0.5rem;
  min-width: 32px;
  height: 32px;
}

/* Quick Filters */
.quick-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.quick-filter-btn {
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-speed) var(--transition-timing);
}

.quick-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Dark Mode for Favorite Filters */
[data-theme="dark"] .favorite-filters-container {
  background: rgba(var(--bg-secondary-rgb), 0.5);
  border-color: var(--border-color);
}

[data-theme="dark"] .favorite-filter-item {
  background: var(--bg-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .favorite-filter-item:hover {
  background: rgba(var(--primary-rgb), 0.1);
  border-color: var(--primary);
}

/* Responsive Favorite Filters */
@media (max-width: 768px) {
  .favorite-filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .favorite-filter-actions {
    justify-content: center;
  }

  .quick-filters {
    justify-content: center;
  }

  .quick-filter-btn {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }
}

/* Enhanced Table Cell Styles */
.expense-type-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.expense-type-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary);
  font-size: 0.8rem;
  transition: all var(--transition-speed) var(--transition-timing);
}

.expense-type-icon:hover {
  background-color: rgba(var(--primary-rgb), 0.2);
  transform: scale(1.1);
}

.amount-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  font-weight: 700;
  color: var(--danger);
}

.amount-icon {
  font-size: 0.9rem;
  opacity: 0.8;
}

.amount-value {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

.date-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  color: var(--text-primary);
}

.date-icon {
  font-size: 0.8rem;
  color: var(--primary);
  opacity: 0.7;
}

.date-value {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.description-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  max-width: 200px;
  margin: 0 auto;
}

.description-icon {
  font-size: 0.8rem;
  opacity: 0.6;
  flex-shrink: 0;
}

.description-value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.action-buttons {
  opacity: 0.7;
  transition: opacity var(--transition-speed) var(--transition-timing);
}

.action-buttons:hover {
  opacity: 1;
}

.action-btn {
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-btn:hover::before {
  width: 100%;
  height: 100%;
}

/* Enhanced Modern Badge Styles */
.modern-badge {
  position: relative;
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 0.7rem;
  padding: 0.4rem 0.8rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modern-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.modern-badge:hover::before {
  left: 100%;
}

/* Table Row Hover Effects */
.modern-table tbody tr {
  transition: all var(--transition-speed) var(--transition-timing);
}

.modern-table tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.modern-table tbody tr:hover .expense-type-icon,
.modern-table tbody tr:hover .amount-icon,
.modern-table tbody tr:hover .date-icon,
.modern-table tbody tr:hover .description-icon {
  transform: scale(1.1);
  opacity: 1;
}

/* Dark Mode Enhancements */
[data-theme="dark"] .expense-type-icon {
  background-color: rgba(var(--primary-rgb), 0.2);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(var(--primary-rgb), 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Accessibility Improvements */
.action-btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.modern-badge:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading Animation for Table Rows */
.staggered-item {
  opacity: 0;
  animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
