import './polyfills.server.mjs';
import {
  AppServerModule,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  resetCompiledComponents,
  setAngularAppManifest
} from "./chunk-UQ5ERDIF.mjs";
import "./chunk-KEEN3TFB.mjs";

// angular:main-server-inject-manifest:angular:main-server-inject-manifest
import manifest from "./angular-app-manifest.mjs";
setAngularAppManifest(manifest);
export {
  AppServerModule as default,
  destroyAngularServerApp as \u0275destroyAngularServerApp,
  extractRoutesAndCreateRouteTree as \u0275extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as \u0275getOrCreateAngularServerApp,
  resetCompiledComponents as \u0275resetCompiledComponents
};
//# sourceMappingURL=main.server.mjs.map
