{"version": 3, "sources": ["src/app/components/expense-management/expense-management.component.css"], "sourcesContent": ["/* Loading Spinner Overlay */\r\n.loading-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  /* Use CSS variable for background with opacity */\r\n  background-color: rgba(var(--bg-primary-rgb, 255, 255, 255), 0.7);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  backdrop-filter: blur(3px); /* Optional blur effect */\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.spinner-container {\r\n  text-align: center;\r\n  animation: zoomIn 0.5s ease-out; /* Add animation */\r\n}\r\n\r\n/* Content Blur when Loading */\r\n.content-blur {\r\n  filter: blur(5px);\r\n  transition: filter 0.3s ease;\r\n}\r\n\r\n/* Modern Stats Card Overrides/Specifics */\r\n.modern-stats-card {\r\n  /* Base styles come from modern-components.css */\r\n  /* Add specific background/icon colors */\r\n  transition: all var(--transition-speed) var(--transition-timing); /* Ensure transition */\r\n}\r\n\r\n.modern-stats-card:hover {\r\n   transform: translateY(-5px);\r\n   box-shadow: var(--shadow-md);\r\n}\r\n\r\n.modern-stats-icon {\r\n   /* Base styles come from modern-components.css */\r\n   color: var(--white); /* Ensure icon color is white */\r\n   box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);\r\n   transition: transform var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.modern-stats-card:hover .modern-stats-icon {\r\n    transform: scale(1.1);\r\n}\r\n\r\n.modern-stats-value {\r\n    /* Base styles come from modern-components.css */\r\n    transition: color var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n/* Daily Expense Card (Example: Info color) */\r\n.daily-expense-card {\r\n  background: linear-gradient(135deg, var(--info-light) 0%, rgba(var(--info-rgb), 0.2) 100%);\r\n}\r\n.daily-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--info) 0%, #0b7b9a 100%); /* Example dark info */\r\n}\r\n.daily-expense-card .modern-stats-value {\r\n  color: var(--info);\r\n}\r\n\r\n/* Monthly Expense Card (Example: Primary color) */\r\n.monthly-expense-card {\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);\r\n}\r\n.monthly-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\r\n}\r\n.monthly-expense-card .modern-stats-value {\r\n  color: var(--primary);\r\n}\r\n\r\n/* Yearly Expense Card (Example: Success color) */\r\n.yearly-expense-card {\r\n  background: linear-gradient(135deg, var(--success-light) 0%, rgba(var(--success-rgb), 0.2) 100%);\r\n}\r\n.yearly-expense-card .modern-stats-icon {\r\n  background: linear-gradient(135deg, var(--success) 0%, #1a6e2f 100%); /* Example dark success */\r\n}\r\n.yearly-expense-card .modern-stats-value {\r\n  color: var(--success);\r\n}\r\n\r\n/*  Eski kart stilleri yorum satırı içinde kalabilir veya tamamen silinebilir */\r\n/*\r\n.count-expense-card { ... }\r\n.avg-expense-card { ... }\r\n.total-expense-card { ... }\r\n*/\r\n\r\n/* Filter Icon Style */\r\n.filter-icon {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem; /* Stil tanımı eklendi */\r\n}\r\n\r\n/* Chart Container */\r\n.chart-container {\r\n  position: relative;\r\n  margin: auto;\r\n  border-radius: var(--border-radius-md);\r\n  padding: var(--spacing-sm); /* Reduced padding */\r\n  background-color: rgba(var(--secondary-rgb), 0.05); /* Subtle background */\r\n  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.04);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  min-height: 300px; /* Ensure container has height */\r\n  display: flex; /* Center empty state */\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.chart-container:hover {\r\n  box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.chart-empty-state {\r\n  color: var(--text-secondary);\r\n  font-style: italic;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n\r\n/* Filter Icon Style */\r\n.filter-icon {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem;\r\n}\r\n\r\n/* Active Filters */\r\n.active-filters {\r\n  margin-bottom: var(--spacing-md);\r\n  padding: var(--spacing-sm) var(--spacing-md);\r\n  background-color: rgba(var(--primary-rgb), 0.05);\r\n  border-radius: var(--border-radius-md);\r\n  border: 1px solid rgba(var(--primary-rgb), 0.1);\r\n}\r\n\r\n.filter-badge {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(var(--primary-rgb), 0.2) 100%);\r\n  color: var(--primary);\r\n  padding: 0.4rem 0.8rem;\r\n  border-radius: var(--border-radius-pill);\r\n  font-size: 0.85rem;\r\n  font-weight: 500;\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.filter-badge:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.15);\r\n}\r\n\r\n.filter-badge .btn-close {\r\n  font-size: 0.65rem;\r\n  padding: 0.25rem;\r\n  margin-left: 0.5rem;\r\n  background-color: rgba(var(--primary-rgb), 0.2);\r\n  border-radius: 50%;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  opacity: 0.7;\r\n}\r\n\r\n.filter-badge .btn-close:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.3);\r\n  transform: rotate(90deg);\r\n  opacity: 1;\r\n}\r\n\r\n\r\n/* Empty State Styles */\r\n.empty-state {\r\n  padding: var(--spacing-xl) var(--spacing-lg);\r\n  text-align: center;\r\n  background-color: var(--bg-secondary);\r\n  border-radius: var(--border-radius-lg);\r\n  margin: var(--spacing-md) 0;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  border: 1px dashed var(--border-color);\r\n}\r\n\r\n.empty-state i,\r\n.empty-state fa-icon {\r\n  font-size: 3rem;\r\n  color: var(--text-secondary);\r\n  opacity: 0.6;\r\n  margin-bottom: var(--spacing-md);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.empty-state:hover i,\r\n.empty-state:hover fa-icon {\r\n  transform: scale(1.1) rotate(-5deg);\r\n  opacity: 0.8;\r\n}\r\n\r\n.empty-state h5 {\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n  margin-bottom: var(--spacing-sm);\r\n  color: var(--text-primary);\r\n}\r\n\r\n.empty-state p {\r\n  color: var(--text-secondary);\r\n  font-size: 1rem;\r\n  max-width: 85%;\r\n  margin: 0 auto var(--spacing-md);\r\n}\r\n\r\n.empty-state .modern-btn {\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  padding: 0.6rem 1.5rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.empty-state .modern-btn:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n/* Table Specifics */\r\n.modern-table td .modern-badge {\r\n  min-width: 80px; /* Ensure badges have some minimum width */\r\n  text-align: center;\r\n}\r\n\r\n.modern-table td[style*=\"font-weight: 700\"] {\r\n   letter-spacing: 0.5px; /* Add slight spacing to amount */\r\n}\r\n\r\n/* Staggered animation for table rows */\r\n.staggered-item {\r\n  animation: fadeInTableRow 0.6s ease-out forwards;\r\n  opacity: 0;\r\n}\r\n\r\n@keyframes fadeInTableRow {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(15px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n/* Ensure icon margins are consistent */\r\n.modern-btn-icon {\r\n    margin-right: 0.5rem;\r\n}\r\n.modern-btn-icon-only fa-icon,\r\n.modern-btn-icon-only i {\r\n    margin-right: 0; /* No margin for icon-only buttons */\r\n}\r\n\r\n/* Dark Mode Adjustments (if needed beyond modern-components.css) */\r\n[data-theme=\"dark\"] .chart-container {\r\n   background-color: rgba(var(--secondary-rgb), 0.1); /* Darker subtle background */\r\n   box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .active-filters {\r\n  background-color: rgba(var(--primary-rgb), 0.1);\r\n  border-color: rgba(var(--primary-rgb), 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .filter-badge {\r\n   background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.15) 0%, rgba(var(--primary-rgb), 0.25) 100%);\r\n   color: var(--primary); /* Primary color should be adjusted for dark mode in variables */\r\n}\r\n\r\n[data-theme=\"dark\"] .filter-badge .btn-close {\r\n   background-color: rgba(var(--primary-rgb), 0.3);\r\n}\r\n[data-theme=\"dark\"] .filter-badge .btn-close:hover {\r\n   background-color: rgba(var(--primary-rgb), 0.4);\r\n}\r\n\r\n/* Search Input Styles (from member.component.css) */\r\n/* Search Input (member.component.css'den alındı) */\r\n.search-input-container {\r\n  position: relative;\r\n  /* margin-bottom: 1rem; */ /* Header içindeki diğer filtrelerle hizalama için kaldırıldı */\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 1rem; /* member.component.css'deki değer */\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: var(--text-muted); /* member.component.css'deki değer */\r\n  /* z-index, font-size, line-height kaldırıldı, gerekirse eklenebilir */\r\n}\r\n\r\n.search-input {\r\n  width: 100%; /* Kapsayıcı genişliğini doldurması için */\r\n  padding: 0.75rem 1rem 0.75rem 2.5rem; /* member.component.css'deki padding (ikon için sol padding dahil) */\r\n  border-radius: 0.5rem; /* member.component.css'deki değer */\r\n  border: 1px solid var(--border-color); /* member.component.css'deki değer */\r\n  background-color: var(--input-bg); /* member.component.css'deki değer */\r\n  color: var(--input-text); /* member.component.css'deki değer */\r\n  transition: all 0.3s ease; /* member.component.css'deki değer */\r\n  /* height, line-height kaldırıldı, padding ile ayarlanıyor */\r\n}\r\n\r\n.search-input:focus {\r\n  border-color: var(--primary-color); /* member.component.css'deki değer */\r\n  box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25); /* member.component.css'deki değer */\r\n  outline: none;\r\n}\r\n\r\n/* Sortable Table Headers */\r\n.sortable {\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  position: relative;\r\n}\r\n\r\n.sortable:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.1);\r\n  color: var(--primary);\r\n}\r\n\r\n.sortable fa-icon {\r\n  opacity: 0.6;\r\n  transition: opacity var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.sortable:hover fa-icon {\r\n  opacity: 1;\r\n}\r\n\r\n/* Pagination Styles */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-top: var(--spacing-lg);\r\n}\r\n\r\n.pagination {\r\n  margin: 0;\r\n}\r\n\r\n.page-item {\r\n  margin: 0 2px;\r\n}\r\n\r\n.page-link {\r\n  border: 1px solid var(--border-color);\r\n  color: var(--text-primary);\r\n  background-color: var(--bg-primary);\r\n  padding: 0.5rem 0.75rem;\r\n  border-radius: var(--border-radius-md);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n  text-decoration: none;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-width: 40px;\r\n  height: 40px;\r\n}\r\n\r\n.page-link:hover:not(:disabled) {\r\n  background-color: var(--primary-light);\r\n  border-color: var(--primary);\r\n  color: var(--primary);\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.page-item.active .page-link {\r\n  background-color: var(--primary);\r\n  border-color: var(--primary);\r\n  color: var(--white);\r\n  font-weight: 600;\r\n}\r\n\r\n.page-item.disabled .page-link {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  background-color: var(--bg-secondary);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.page-item.disabled .page-link:hover {\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* Table Info and Page Size Selector */\r\n.table-info {\r\n  font-size: 0.9rem;\r\n  color: var(--text-secondary);\r\n}\r\n\r\n.page-size-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.page-size-selector .form-label {\r\n  font-size: 0.9rem;\r\n  color: var(--text-secondary);\r\n  white-space: nowrap;\r\n}\r\n\r\n/* Advanced Filters */\r\n.modern-card-body.border-bottom {\r\n  border-bottom: 1px solid var(--border-color) !important;\r\n}\r\n\r\n/* Dark Mode Pagination */\r\n[data-theme=\"dark\"] .page-link {\r\n  background-color: var(--bg-secondary);\r\n  border-color: var(--border-color);\r\n  color: var(--text-primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .page-link:hover:not(:disabled) {\r\n  background-color: rgba(var(--primary-rgb), 0.2);\r\n  border-color: var(--primary);\r\n  color: var(--primary);\r\n}\r\n\r\n[data-theme=\"dark\"] .page-item.active .page-link {\r\n  background-color: var(--primary);\r\n  border-color: var(--primary);\r\n  color: var(--white);\r\n}\r\n\r\n[data-theme=\"dark\"] .page-item.disabled .page-link {\r\n  background-color: rgba(var(--bg-secondary-rgb), 0.5);\r\n  color: var(--text-secondary);\r\n}\r\n\r\n/* Trend Indicators */\r\n.trend-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  margin-top: 0.5rem;\r\n  font-size: 0.85rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.trend-indicator i {\r\n  font-size: 0.9rem;\r\n  animation: pulse 2s infinite;\r\n}\r\n\r\n.trend-text {\r\n  font-weight: 600;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    transform: scale(1.1);\r\n  }\r\n  100% {\r\n    transform: scale(1);\r\n  }\r\n}\r\n\r\n/* Enhanced Chart Containers */\r\n.chart-container canvas {\r\n  border-radius: var(--border-radius-md);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.chart-container:hover canvas {\r\n  transform: scale(1.02);\r\n  box-shadow: var(--shadow-lg);\r\n}\r\n\r\n/* Chart Loading State */\r\n.chart-loading {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 300px;\r\n  color: var(--text-secondary);\r\n  font-style: italic;\r\n}\r\n\r\n.chart-loading i {\r\n  margin-right: 0.5rem;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Enhanced Modern Stats Cards */\r\n.modern-stats-card {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.modern-stats-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\r\n  transform: translateX(-100%);\r\n  transition: transform 0.6s ease;\r\n}\r\n\r\n.modern-stats-card:hover::before {\r\n  transform: translateX(100%);\r\n}\r\n\r\n/* Enhanced Responsive Design */\r\n@media (max-width: 1200px) {\r\n  .modern-card-header {\r\n    flex-direction: column;\r\n    gap: 1rem;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .modern-card-header > div {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .expense-type-cell {\r\n    flex-direction: column;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .amount-cell,\r\n  .date-cell,\r\n  .description-cell {\r\n    flex-direction: column;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .modern-table th,\r\n  .modern-table td {\r\n    padding: 0.5rem 0.25rem;\r\n    font-size: 0.85rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .trend-indicator {\r\n    font-size: 0.75rem;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .trend-indicator i {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .chart-container {\r\n    min-height: 250px;\r\n  }\r\n\r\n  .pagination {\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n\r\n  .page-item {\r\n    margin: 2px;\r\n  }\r\n\r\n  /* Mobile table improvements */\r\n  .modern-table {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .modern-table th,\r\n  .modern-table td {\r\n    padding: 0.4rem 0.2rem;\r\n  }\r\n\r\n  .action-buttons {\r\n    flex-direction: column;\r\n    gap: 0.25rem;\r\n  }\r\n\r\n  .action-btn {\r\n    padding: 0.25rem;\r\n    min-width: 32px;\r\n    height: 32px;\r\n  }\r\n\r\n  /* Mobile filters */\r\n  .row.g-3 {\r\n    --bs-gutter-x: 0.5rem;\r\n  }\r\n\r\n  .modern-form-control-sm {\r\n    font-size: 0.8rem;\r\n    padding: 0.4rem 0.6rem;\r\n  }\r\n}\r\n\r\n@media (max-width: 576px) {\r\n  .modern-stats-card {\r\n    margin-bottom: 1rem;\r\n  }\r\n\r\n  .modern-stats-value {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .modern-stats-label {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  /* Stack table on very small screens */\r\n  .table-responsive {\r\n    border: none;\r\n  }\r\n\r\n  .modern-table,\r\n  .modern-table thead,\r\n  .modern-table tbody,\r\n  .modern-table th,\r\n  .modern-table td,\r\n  .modern-table tr {\r\n    display: block;\r\n  }\r\n\r\n  .modern-table thead tr {\r\n    position: absolute;\r\n    top: -9999px;\r\n    left: -9999px;\r\n  }\r\n\r\n  .modern-table tr {\r\n    border: 1px solid var(--border-color);\r\n    border-radius: var(--border-radius-md);\r\n    margin-bottom: 1rem;\r\n    padding: 1rem;\r\n    background: var(--bg-primary);\r\n    box-shadow: var(--shadow-sm);\r\n  }\r\n\r\n  .modern-table td {\r\n    border: none;\r\n    position: relative;\r\n    padding: 0.5rem 0;\r\n    text-align: left !important;\r\n  }\r\n\r\n  .modern-table td:before {\r\n    content: attr(data-label) \": \";\r\n    font-weight: bold;\r\n    color: var(--text-secondary);\r\n    display: inline-block;\r\n    width: 100px;\r\n    margin-right: 0.5rem;\r\n  }\r\n\r\n  .expense-type-cell,\r\n  .amount-cell,\r\n  .date-cell,\r\n  .description-cell {\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .action-buttons {\r\n    justify-content: flex-start;\r\n    margin-top: 0.5rem;\r\n  }\r\n\r\n  /* Mobile pagination */\r\n  .pagination {\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .page-link {\r\n    padding: 0.4rem 0.6rem;\r\n    min-width: 32px;\r\n    height: 32px;\r\n  }\r\n}\r\n\r\n/* Performance optimizations */\r\n.modern-table tbody tr {\r\n  will-change: transform;\r\n}\r\n\r\n.staggered-item {\r\n  will-change: opacity, transform;\r\n}\r\n\r\n.chart-container canvas {\r\n  will-change: transform;\r\n}\r\n\r\n/* Reduce motion for users who prefer it */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .staggered-item,\r\n  .modern-table tbody tr,\r\n  .chart-container canvas,\r\n  .action-btn,\r\n  .modern-badge {\r\n    animation: none !important;\r\n    transition: none !important;\r\n  }\r\n\r\n  .trend-indicator i {\r\n    animation: none !important;\r\n  }\r\n}\r\n\r\n/* Favorite Filters Styles */\r\n.favorite-filters-container {\r\n  background: var(--bg-secondary);\r\n  border: 1px solid var(--border-color);\r\n  border-radius: var(--border-radius-md);\r\n  padding: 1rem;\r\n  margin-top: 0.5rem;\r\n}\r\n\r\n.favorite-filters-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.favorite-filter-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 0.75rem;\r\n  margin-bottom: 0.5rem;\r\n  background: var(--bg-primary);\r\n  border: 1px solid var(--border-color);\r\n  border-radius: var(--border-radius-sm);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.favorite-filter-item:hover {\r\n  background: rgba(var(--primary-rgb), 0.05);\r\n  border-color: var(--primary);\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-sm);\r\n}\r\n\r\n.favorite-filter-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.favorite-filter-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 0.25rem;\r\n}\r\n\r\n.favorite-filter-name {\r\n  font-weight: 600;\r\n  color: var(--text-primary);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.favorite-filter-date {\r\n  color: var(--text-secondary);\r\n  font-size: 0.75rem;\r\n}\r\n\r\n.favorite-filter-actions {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n}\r\n\r\n.favorite-filter-actions .modern-btn {\r\n  padding: 0.25rem 0.5rem;\r\n  min-width: 32px;\r\n  height: 32px;\r\n}\r\n\r\n/* Quick Filters */\r\n.quick-filters {\r\n  display: flex;\r\n  gap: 0.5rem;\r\n  flex-wrap: wrap;\r\n  margin-bottom: 1rem;\r\n}\r\n\r\n.quick-filter-btn {\r\n  padding: 0.4rem 0.8rem;\r\n  font-size: 0.8rem;\r\n  border-radius: var(--border-radius-lg);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.quick-filter-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n/* Dark Mode for Favorite Filters */\r\n[data-theme=\"dark\"] .favorite-filters-container {\r\n  background: rgba(var(--bg-secondary-rgb), 0.5);\r\n  border-color: var(--border-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .favorite-filter-item {\r\n  background: var(--bg-primary);\r\n  border-color: var(--border-color);\r\n}\r\n\r\n[data-theme=\"dark\"] .favorite-filter-item:hover {\r\n  background: rgba(var(--primary-rgb), 0.1);\r\n  border-color: var(--primary);\r\n}\r\n\r\n/* Responsive Favorite Filters */\r\n@media (max-width: 768px) {\r\n  .favorite-filter-item {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 0.5rem;\r\n  }\r\n\r\n  .favorite-filter-actions {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-filters {\r\n    justify-content: center;\r\n  }\r\n\r\n  .quick-filter-btn {\r\n    font-size: 0.75rem;\r\n    padding: 0.3rem 0.6rem;\r\n  }\r\n}\r\n\r\n/* Enhanced Table Cell Styles */\r\n.expense-type-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.5rem;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.expense-type-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 24px;\r\n  height: 24px;\r\n  border-radius: 50%;\r\n  background-color: rgba(var(--primary-rgb), 0.1);\r\n  color: var(--primary);\r\n  font-size: 0.8rem;\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.expense-type-icon:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.amount-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.25rem;\r\n  font-weight: 700;\r\n  color: var(--danger);\r\n}\r\n\r\n.amount-icon {\r\n  font-size: 0.9rem;\r\n  opacity: 0.8;\r\n}\r\n\r\n.amount-value {\r\n  font-family: 'Courier New', monospace;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.date-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.25rem;\r\n  color: var(--text-primary);\r\n}\r\n\r\n.date-icon {\r\n  font-size: 0.8rem;\r\n  color: var(--primary);\r\n  opacity: 0.7;\r\n}\r\n\r\n.date-value {\r\n  font-family: 'Courier New', monospace;\r\n  font-weight: 500;\r\n}\r\n\r\n.description-cell {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 0.25rem;\r\n  max-width: 200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.description-icon {\r\n  font-size: 0.8rem;\r\n  opacity: 0.6;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.description-value {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n  flex: 1;\r\n}\r\n\r\n.action-buttons {\r\n  opacity: 0.7;\r\n  transition: opacity var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.action-buttons:hover {\r\n  opacity: 1;\r\n}\r\n\r\n.action-btn {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.action-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 0;\r\n  height: 0;\r\n  background-color: rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transition: width 0.3s ease, height 0.3s ease;\r\n}\r\n\r\n.action-btn:hover::before {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* Enhanced Modern Badge Styles */\r\n.modern-badge {\r\n  position: relative;\r\n  overflow: hidden;\r\n  font-weight: 600;\r\n  letter-spacing: 0.5px;\r\n  text-transform: uppercase;\r\n  font-size: 0.7rem;\r\n  padding: 0.4rem 0.8rem;\r\n  border-radius: var(--border-radius-lg);\r\n  box-shadow: var(--shadow-sm);\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.modern-badge:hover {\r\n  transform: translateY(-1px);\r\n  box-shadow: var(--shadow-md);\r\n}\r\n\r\n.modern-badge::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.modern-badge:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n/* Table Row Hover Effects */\r\n.modern-table tbody tr {\r\n  transition: all var(--transition-speed) var(--transition-timing);\r\n}\r\n\r\n.modern-table tbody tr:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.05);\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.modern-table tbody tr:hover .expense-type-icon,\r\n.modern-table tbody tr:hover .amount-icon,\r\n.modern-table tbody tr:hover .date-icon,\r\n.modern-table tbody tr:hover .description-icon {\r\n  transform: scale(1.1);\r\n  opacity: 1;\r\n}\r\n\r\n/* Dark Mode Enhancements */\r\n[data-theme=\"dark\"] .expense-type-icon {\r\n  background-color: rgba(var(--primary-rgb), 0.2);\r\n}\r\n\r\n[data-theme=\"dark\"] .modern-table tbody tr:hover {\r\n  background-color: rgba(var(--primary-rgb), 0.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* Accessibility Improvements */\r\n.action-btn:focus {\r\n  outline: 2px solid var(--primary);\r\n  outline-offset: 2px;\r\n}\r\n\r\n.modern-badge:focus {\r\n  outline: 2px solid var(--primary);\r\n  outline-offset: 2px;\r\n}\r\n\r\n/* Loading Animation for Table Rows */\r\n.staggered-item {\r\n  opacity: 0;\r\n  animation: slideInUp 0.6s ease forwards;\r\n}\r\n\r\n@keyframes slideInUp {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n"], "mappings": ";AACA,CAAC;AACC,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AAER,oBAAkB,KAAK,IAAI,gBAAgB,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AAC7D,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,2BAAiB,KAAK;AAAtB,mBAAiB,KAAK;AACtB,cAAY,QAAQ,KAAK;AAC3B;AAEA,CAAC;AACC,cAAY;AACZ,aAAW,OAAO,KAAK;AACzB;AAGA,CAAC;AACC,UAAQ,KAAK;AACb,cAAY,OAAO,KAAK;AAC1B;AAGA,CAAC;AAGC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CANC,iBAMiB;AACf,aAAW,WAAW;AACtB,cAAY,IAAI;AACnB;AAEA,CAAC;AAEE,SAAO,IAAI;AACX,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACrC,cAAY,UAAU,IAAI,oBAAoB,IAAI;AACrD;AAEA,CAlBC,iBAkBiB,OAAO,CAPxB;AAQG,aAAW,MAAM;AACrB;AAEA,CAAC;AAEG,cAAY,MAAM,IAAI,oBAAoB,IAAI;AAClD;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,cAAc,EAAE;AAAA,MAAE,KAAK,IAAI,WAAW,EAAE,KAAK;AACvF;AACA,CAHC,mBAGmB,CApBnB;AAqBC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,QAAQ,EAAE;AAAA,MAAE,QAAQ;AAC9D;AACA,CANC,mBAMmB,CAZnB;AAaC,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC7F;AACA,CAHC,qBAGqB,CA/BrB;AAgCC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,IAAI,gBAAgB;AAC7E;AACA,CANC,qBAMqB,CAvBrB;AAwBC,SAAO,IAAI;AACb;AAGA,CAAC;AACC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC7F;AACA,CAHC,oBAGoB,CA1CpB;AA2CC;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,WAAW,EAAE;AAAA,MAAE,QAAQ;AACjE;AACA,CANC,oBAMoB,CAlCpB;AAmCC,SAAO,IAAI;AACb;AAUA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,YAAU;AACV,UAAQ;AACR,iBAAe,IAAI;AACnB,WAAS,IAAI;AACb,oBAAkB,KAAK,IAAI,gBAAgB,EAAE;AAC7C,cAAY,MAAM,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACxC,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,cAAY;AACZ,WAAS;AACT,eAAa;AACb,mBAAiB;AACnB;AAEA,CAdC,eAce;AACd,cAAY,MAAM,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C;AAEA,CAAC;AACC,SAAO,IAAI;AACX,cAAY;AACZ,aAAW;AACb;AAIA,CAhCC;AAiCC,SAAO,IAAI;AACX,aAAW;AACb;AAGA,CAAC;AACC,iBAAe,IAAI;AACnB,WAAS,IAAI,cAAc,IAAI;AAC/B,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,iBAAe,IAAI;AACnB,UAAQ,IAAI,MAAM,KAAK,IAAI,cAAc,EAAE;AAC7C;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb;AAAA,IAAY;AAAA,MAAgB,MAAM;AAAA,MAAE,IAAI,iBAAiB,EAAE;AAAA,MAAE,KAAK,IAAI,cAAc,EAAE,KAAK;AAC3F,SAAO,IAAI;AACX,WAAS,OAAO;AAChB,iBAAe,IAAI;AACnB,aAAW;AACX,eAAa;AACb,cAAY,IAAI;AAChB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAbC,YAaY;AACX,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,IAAI,KAAK,IAAI,cAAc,EAAE;AACjD;AAEA,CAlBC,aAkBa,CAAC;AACb,aAAW;AACX,WAAS;AACT,eAAa;AACb,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,iBAAe;AACf,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,WAAS;AACX;AAEA,CA5BC,aA4Ba,CAVC,SAUS;AACtB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,aAAW,OAAO;AAClB,WAAS;AACX;AAIA,CAAC;AACC,WAAS,IAAI,cAAc,IAAI;AAC/B,cAAY;AACZ,oBAAkB,IAAI;AACtB,iBAAe,IAAI;AACnB,UAAQ,IAAI,cAAc;AAC1B,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,UAAQ,IAAI,OAAO,IAAI;AACzB;AAEA,CAVC,YAUY;AACb,CAXC,YAWY;AACX,aAAW;AACX,SAAO,IAAI;AACX,WAAS;AACT,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAnBC,WAmBW,OAAO;AACnB,CApBC,WAoBW,OAAO;AACjB,aAAW,MAAM,KAAK,OAAO;AAC7B,WAAS;AACX;AAEA,CAzBC,YAyBY;AACX,aAAW;AACX,eAAa;AACb,iBAAe,IAAI;AACnB,SAAO,IAAI;AACb;AAEA,CAhCC,YAgCY;AACX,SAAO,IAAI;AACX,aAAW;AACX,aAAW;AACX,UAAQ,EAAE,KAAK,IAAI;AACrB;AAEA,CAvCC,YAuCY,CAAC;AACZ,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,WAAS,OAAO;AAChB,eAAa;AACf;AAEA,CA7CC,YA6CY,CANC,UAMU;AACtB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAGA,CAAC,aAAa,GAAG,CAAC;AAChB,aAAW;AACX,cAAY;AACd;AAEA,CALC,aAKa,EAAE,CAAC;AACd,kBAAgB;AACnB;AAGA,CAAC;AACC,aAAW,eAAe,KAAK,SAAS;AACxC,WAAS;AACX;AAEA,WAJa;AAKX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;AAGA,CAAC;AACG,gBAAc;AAClB;AACA,CAAC,qBAAqB;AACtB,CADC,qBACqB;AAClB,gBAAc;AAClB;AAGA,CAAC,iBAAmB,CAnKnB;AAoKE,oBAAkB,KAAK,IAAI,gBAAgB,EAAE;AAC7C,cAAY,MAAM,EAAE,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C;AAEA,CAAC,iBAAmB,CAxInB;AAyIC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,gBAAc,KAAK,IAAI,cAAc,EAAE;AACzC;AAEA,CAAC,iBAAmB,CArInB;AAsIE;AAAA,IAAY;AAAA,MAAgB,MAAhB;AAAA,MAAwB,KAAK,IAAI,cAAc,EAAE,MAAM,EAAvD;AAAA,MAA2D,KAAK,IAAI,cAAc,EAAE,MAAM;AACtG,SAAO,IAAI;AACd;AAEA,CAAC,iBAAmB,CA1InB,aA0IiC,CAxHnB;AAyHZ,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC9C;AACA,CAAC,iBAAmB,CA7InB,aA6IiC,CA3HnB,SA2H6B;AACzC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC9C;AAIA,CAAC;AACC,YAAU;AAEZ;AAEA,CAAC;AACC,YAAU;AACV,QAAM;AACN,OAAK;AACL,aAAW,WAAW;AACtB,SAAO,IAAI;AAEb;AAEA,CAAC;AACC,SAAO;AACP,WAAS,QAAQ,KAAK,QAAQ;AAC9B,iBAAe;AACf,UAAQ,IAAI,MAAM,IAAI;AACtB,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACX,cAAY,IAAI,KAAK;AAEvB;AAEA,CAXC,YAWY;AACX,gBAAc,IAAI;AAClB,cAAY,EAAE,EAAE,EAAE,OAAO,KAAK,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE;AAC3C,WAAS;AACX;AAGA,CAAC;AACC,UAAQ;AACR,uBAAa;AAAb,eAAa;AACb,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,YAAU;AACZ;AAEA,CAPC,QAOQ;AACP,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,SAAO,IAAI;AACb;AAEA,CAZC,SAYS;AACR,WAAS;AACT,cAAY,QAAQ,IAAI,oBAAoB,IAAI;AAClD;AAEA,CAjBC,QAiBQ,OAAO;AACd,WAAS;AACX;AAGA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,cAAY,IAAI;AAClB;AAEA,CAAC;AACC,UAAQ;AACV;AAEA,CAAC;AACC,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,UAAQ,IAAI,MAAM,IAAI;AACtB,SAAO,IAAI;AACX,oBAAkB,IAAI;AACtB,WAAS,OAAO;AAChB,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC5C,mBAAiB;AACjB,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,aAAW;AACX,UAAQ;AACV;AAEA,CAfC,SAeS,MAAM,KAAK;AACnB,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACX,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CA3BC,SA2BS,CAAC,OAAO,CAvBjB;AAwBC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACX,eAAa;AACf;AAEA,CAlCC,SAkCS,CAAC,SAAS,CA9BnB;AA+BC,WAAS;AACT,UAAQ;AACR,oBAAkB,IAAI;AACtB,SAAO,IAAI;AACb;AAEA,CAzCC,SAyCS,CAPC,SAOS,CArCnB,SAqC6B;AAC5B,aAAW;AACX,cAAY;AACd;AAGA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACP;AAEA,CANC,mBAMmB,CAAC;AACnB,aAAW;AACX,SAAO,IAAI;AACX,eAAa;AACf;AAGA,CAAC,gBAAgB,CAAC;AAChB,iBAAe,IAAI,MAAM,IAAI;AAC/B;AAGA,CAAC,iBAAmB,CAlEnB;AAmEC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAxEnB,SAwE6B,MAAM,KAAK;AACvC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAlFnB,SAkF6B,CAvDnB,OAuD2B,CA9ErC;AA+EC,oBAAkB,IAAI;AACtB,gBAAc,IAAI;AAClB,SAAO,IAAI;AACb;AAEA,CAAC,iBAAmB,CAxFnB,SAwF6B,CAtDnB,SAsD6B,CApFvC;AAqFC,oBAAkB,KAAK,IAAI,mBAAmB,EAAE;AAChD,SAAO,IAAI;AACb;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,OAAK;AACL,cAAY;AACZ,aAAW;AACX,eAAa;AACf;AAEA,CATC,gBASgB;AACf,aAAW;AACX,aAAW,MAAM,GAAG;AACtB;AAEA,CAAC;AACC,eAAa;AACb,kBAAgB;AAChB,kBAAgB;AAClB;AAEA,WATa;AAUX;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACA;AACE,eAAW,MAAM;AACnB;AACF;AAGA,CA3XC,gBA2XgB;AACf,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAhYC,eAgYe,OAAO;AACrB,aAAW,MAAM;AACjB,cAAY,IAAI;AAClB;AAGA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,UAAQ;AACR,SAAO,IAAI;AACX,cAAY;AACd;AAEA,CATC,cASc;AACb,gBAAc;AACd,aAAW,KAAK,GAAG,OAAO;AAC5B;AAEA,WAHa;AAIX;AAAK,eAAW,OAAO;AAAO;AAC9B;AAAO,eAAW,OAAO;AAAS;AACpC;AAGA,CApeC;AAqeC,YAAU;AACV,YAAU;AACZ;AAEA,CAzeC,iBAyeiB;AAChB,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,WAAW;AAAA,MAAE,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAAA,MAAE;AAC1E,aAAW,WAAW;AACtB,cAAY,UAAU,KAAK;AAC7B;AAEA,CArfC,iBAqfiB,MAAM;AACtB,aAAW,WAAW;AACxB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAChB,SAAK;AACL,iBAAa;AACf;AAEA,GANC,mBAMmB,EAAE;AACpB,qBAAiB;AACnB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAAC;AACC,oBAAgB;AAChB,SAAK;AACP;AAEA,GAAC;AAAA,EACD,CAAC;AAAA,EACD,CAAC;AACC,oBAAgB;AAChB,SAAK;AACP;AAEA,GA1UD,aA0Ue;AAAA,EACd,CA3UD,aA2Ue;AACZ,aAAS,OAAO;AAChB,eAAW;AACb;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAvHD;AAwHG,eAAW;AACX,SAAK;AACP;AAEA,GA5HD,gBA4HkB;AACf,eAAW;AACb;AAEA,GA1dD;AA2dG,gBAAY;AACd;AAEA,GAtOD;AAuOG,eAAW;AACX,qBAAiB;AACnB;AAEA,GAvOD;AAwOG,YAAQ;AACV;AAGA,GAzWD;AA0WG,eAAW;AACb;AAEA,GA7WD,aA6We;AAAA,EACd,CA9WD,aA8We;AACZ,aAAS,OAAO;AAClB;AAEA,GAAC;AACC,oBAAgB;AAChB,SAAK;AACP;AAEA,GAAC;AACC,aAAS;AACT,eAAW;AACX,YAAQ;AACV;AAGA,GAAC,GAAG,CAAC;AACH,mBAAe;AACjB;AAEA,GAAC;AACC,eAAW;AACX,aAAS,OAAO;AAClB;AACF;AAEA,OAAO,CAAC,SAAS,EAAE;AACjB,GAllBD;AAmlBG,mBAAe;AACjB;AAEA,GAhkBD;AAikBG,eAAW;AACb;AAEA,GAAC;AACC,eAAW;AACb;AAGA,GAAC;AACC,YAAQ;AACV;AAEA,GA1ZD;AAAA,EA2ZC,CA3ZD,aA2Ze;AAAA,EACd,CA5ZD,aA4Ze;AAAA,EACd,CA7ZD,aA6Ze;AAAA,EACd,CA9ZD,aA8Ze;AAAA,EACd,CA/ZD,aA+Ze;AACZ,aAAS;AACX;AAEA,GAnaD,aAmae,MAAM;AAClB,cAAU;AACV,SAAK;AACL,UAAM;AACR;AAEA,GAzaD,aAyae;AACZ,YAAQ,IAAI,MAAM,IAAI;AACtB,mBAAe,IAAI;AACnB,mBAAe;AACf,aAAS;AACT,gBAAY,IAAI;AAChB,gBAAY,IAAI;AAClB;AAEA,GAlbD,aAkbe;AACZ,YAAQ;AACR,cAAU;AACV,aAAS,OAAO;AAChB,gBAAY;AACd;AAEA,GAzbD,aAybe,EAAE;AACd,aAAS,KAAK,YAAY;AAC1B,iBAAa;AACb,WAAO,IAAI;AACX,aAAS;AACT,WAAO;AACP,kBAAc;AAChB;AAEA,GApIC;AAAA,EAqID,CAhIC;AAAA,EAiID,CAhIC;AAAA,EAiID,CAhIC;AAiIC,qBAAiB;AACnB;AAEA,GAvFC;AAwFC,qBAAiB;AACjB,gBAAY;AACd;AAGA,GAtVD;AAuVG,eAAW;AACb;AAEA,GAlVD;AAmVG,aAAS,OAAO;AAChB,eAAW;AACX,YAAQ;AACV;AACF;AAGA,CA3dC,aA2da,MAAM;AAClB,eAAa;AACf;AAEA,CArdC;AAsdC,eAAa,OAAO,EAAE;AACxB;AAEA,CAlmBC,gBAkmBgB;AACf,eAAa;AACf;AAGA,OAAO,CAAC,sBAAsB,EAAE;AAC9B,GA/dD;AAAA,EAgeC,CA1eD,aA0ee,MAAM;AAAA,EACpB,CA1mBD,gBA0mBkB;AAAA,EACjB,CArHC;AAAA,EAsHD,CA7egB;AA8ed,eAAW;AACX,gBAAY;AACd;AAEA,GAvRD,gBAuRkB;AACf,eAAW;AACb;AACF;AAGA,CAAC;AACC,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,WAAS;AACT,cAAY;AACd;AAEA,CAAC;AACC,cAAY;AACZ,cAAY;AACd;AAEA,CAAC;AACC,WAAS;AACT,mBAAiB;AACjB,eAAa;AACb,WAAS;AACT,iBAAe;AACf,cAAY,IAAI;AAChB,UAAQ,IAAI,MAAM,IAAI;AACtB,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAZC,oBAYoB;AACnB,cAAY,KAAK,IAAI,cAAc,EAAE;AACrC,gBAAc,IAAI;AAClB,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CAnBC,oBAmBoB;AACnB,iBAAe;AACjB;AAEA,CAAC;AACC,QAAM;AACN,WAAS;AACT,kBAAgB;AAChB,OAAK;AACP;AAEA,CAAC;AACC,eAAa;AACb,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,SAAO,IAAI;AACX,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,OAAK;AACP;AAEA,CALC,wBAKwB,CA/jBX;AAgkBZ,WAAS,QAAQ;AACjB,aAAW;AACX,UAAQ;AACV;AAGA,CAAC;AACC,WAAS;AACT,OAAK;AACL,aAAW;AACX,iBAAe;AACjB;AAEA,CAAC;AACC,WAAS,OAAO;AAChB,aAAW;AACX,iBAAe,IAAI;AACnB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAPC,gBAOgB;AACf,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAGA,CAAC,iBAAmB,CAtFnB;AAuFC,cAAY,KAAK,IAAI,mBAAmB,EAAE;AAC1C,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CA9EnB;AA+EC,cAAY,IAAI;AAChB,gBAAc,IAAI;AACpB;AAEA,CAAC,iBAAmB,CAnFnB,oBAmFwC;AACvC,cAAY,KAAK,IAAI,cAAc,EAAE;AACrC,gBAAc,IAAI;AACpB;AAGA,OAAO,CAAC,SAAS,EAAE;AACjB,GA1FD;AA2FG,oBAAgB;AAChB,iBAAa;AACb,SAAK;AACP;AAEA,GAvDD;AAwDG,qBAAiB;AACnB;AAEA,GA/CD;AAgDG,qBAAiB;AACnB;AAEA,GA5CD;AA6CG,eAAW;AACX,aAAS,OAAO;AAClB;AACF;AAGA,CAtTG;AAuTD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,aAAW;AACb;AAEA,CAAC;AACC,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,SAAO;AACP,UAAQ;AACR,iBAAe;AACf,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,SAAO,IAAI;AACX,aAAW;AACX,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAbC,iBAaiB;AAChB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,aAAW,MAAM;AACnB;AAEA,CA3UG;AA4UD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,eAAa;AACb,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACX;AAEA,CAAC;AACC,eAAa,aAAa,EAAE;AAC5B,kBAAgB;AAClB;AAEA,CA7VG;AA8VD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,SAAO,IAAI;AACb;AAEA,CAAC;AACC,aAAW;AACX,SAAO,IAAI;AACX,WAAS;AACX;AAEA,CAAC;AACC,eAAa,aAAa,EAAE;AAC5B,eAAa;AACf;AAEA,CA/WG;AAgXD,WAAS;AACT,eAAa;AACb,mBAAiB;AACjB,OAAK;AACL,aAAW;AACX,UAAQ,EAAE;AACZ;AAEA,CAAC;AACC,aAAW;AACX,WAAS;AACT,eAAa;AACf;AAEA,CAAC;AACC,YAAU;AACV,iBAAe;AACf,eAAa;AACb,QAAM;AACR;AAEA,CAxVG;AAyVD,WAAS;AACT,cAAY,QAAQ,IAAI,oBAAoB,IAAI;AAClD;AAEA,CA7VG,cA6VY;AACb,WAAS;AACX;AAEA,CA5VG;AA6VD,YAAU;AACV,YAAU;AACZ;AAEA,CAjWG,UAiWQ;AACT,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR,oBAAkB,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACtC,iBAAe;AACf,aAAW,UAAU,IAAI,EAAE;AAC3B,cAAY,MAAM,KAAK,IAAI,EAAE,OAAO,KAAK;AAC3C;AAEA,CA9WG,UA8WQ,MAAM;AACf,SAAO;AACP,UAAQ;AACV;AAGA,CA3uBkB;AA4uBhB,YAAU;AACV,YAAU;AACV,eAAa;AACb,kBAAgB;AAChB,kBAAgB;AAChB,aAAW;AACX,WAAS,OAAO;AAChB,iBAAe,IAAI;AACnB,cAAY,IAAI;AAChB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAxvBkB,YAwvBL;AACX,aAAW,WAAW;AACtB,cAAY,IAAI;AAClB;AAEA,CA7vBkB,YA6vBL;AACX,WAAS;AACT,YAAU;AACV,OAAK;AACL,QAAM;AACN,SAAO;AACP,UAAQ;AACR;AAAA,IAAY;AAAA,MAAgB,KAAK;AAAA,MAAE,WAAW;AAAA,MAAE,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;AAAA,MAAE;AAC1E,cAAY,KAAK,KAAK;AACxB;AAEA,CAxwBkB,YAwwBL,MAAM;AACjB,QAAM;AACR;AAGA,CA7wBC,aA6wBa,MAAM;AAClB,cAAY,IAAI,IAAI,oBAAoB,IAAI;AAC9C;AAEA,CAjxBC,aAixBa,MAAM,EAAE;AACpB,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,aAAW,WAAW;AACtB,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAEA,CAvxBC,aAuxBa,MAAM,EAAE,OAAO,CA3J5B;AA4JD,CAxxBC,aAwxBa,MAAM,EAAE,OAAO,CAjI5B;AAkID,CAzxBC,aAyxBa,MAAM,EAAE,OAAO,CAhH5B;AAiHD,CA1xBC,aA0xBa,MAAM,EAAE,OAAO,CA7F5B;AA8FC,aAAW,MAAM;AACjB,WAAS;AACX;AAGA,CAAC,iBAAmB,CApKnB;AAqKC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC7C;AAEA,CAAC,iBAAmB,CApyBnB,aAoyBiC,MAAM,EAAE;AACxC,oBAAkB,KAAK,IAAI,cAAc,EAAE;AAC3C,cAAY,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACvC;AAGA,CAnbG,UAmbQ;AACT,WAAS,IAAI,MAAM,IAAI;AACvB,kBAAgB;AAClB;AAEA,CA/yBkB,YA+yBL;AACX,WAAS,IAAI,MAAM,IAAI;AACvB,kBAAgB;AAClB;AAGA,CA3yBC;AA4yBC,WAAS;AACT,aAAW,UAAU,KAAK,KAAK;AACjC;AAEA,WAHa;AAIX;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACA;AACE,aAAS;AACT,eAAW,WAAW;AACxB;AACF;", "names": []}