using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext; // ICompanyContext için eklendi
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExpenseDal : EfCompanyEntityRepositoryBase<Expense, GymContext>, IExpenseDal
    {
        private readonly ICompanyContext _companyContext;

        public EfExpenseDal(ICompanyContext companyContext) : base(companyContext)
        {
            _companyContext = companyContext;
        }

        public PaginatedResult<ExpenseDto> GetExpensesPaginated(ExpensePagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                var companyId = _companyContext.GetCompanyId();
                var query = context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive);

                // Filtreleme
                query = ApplyFilters(query, parameters);

                // Toplam kayıt sayısı
                var totalCount = query.Count();

                // Sıralama
                query = ApplySorting(query, parameters);

                // Sayfalama
                var expenses = query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .ToList();

                return new PaginatedResult<ExpenseDto>
                {
                    Data = expenses,
                    PageNumber = parameters.PageNumber,
                    PageSize = parameters.PageSize,
                    TotalCount = totalCount,
                    TotalPages = (int)Math.Ceiling((double)totalCount / parameters.PageSize),
                    HasPrevious = parameters.PageNumber > 1,
                    HasNext = parameters.PageNumber < (int)Math.Ceiling((double)totalCount / parameters.PageSize)
                };
            }
        }

        public List<ExpenseDto> GetAllExpensesFiltered(ExpensePagingParameters parameters)
        {
            using (var context = new GymContext())
            {
                var companyId = _companyContext.GetCompanyId();
                var query = context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive);

                // Filtreleme
                query = ApplyFilters(query, parameters);

                // Sıralama
                query = ApplySorting(query, parameters);

                return query
                    .Select(e => new ExpenseDto
                    {
                        ExpenseID = e.ExpenseID,
                        CompanyID = e.CompanyID,
                        Description = e.Description,
                        Amount = e.Amount,
                        ExpenseDate = e.ExpenseDate,
                        ExpenseType = e.ExpenseType,
                        CreationDate = e.CreationDate
                    })
                    .ToList();
            }
        }

        private IQueryable<Expense> ApplyFilters(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            // Arama metni filtresi
            if (!string.IsNullOrEmpty(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                query = query.Where(e =>
                    (e.Description != null && e.Description.ToLower().Contains(searchText)) ||
                    (e.ExpenseType != null && e.ExpenseType.ToLower().Contains(searchText)));
            }

            // Tarih aralığı filtresi
            if (parameters.StartDate.HasValue)
            {
                query = query.Where(e => e.ExpenseDate >= parameters.StartDate.Value);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDate = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                query = query.Where(e => e.ExpenseDate <= endDate);
            }

            // Gider türü filtresi
            if (!string.IsNullOrEmpty(parameters.ExpenseType))
            {
                query = query.Where(e => e.ExpenseType == parameters.ExpenseType);
            }

            // Tutar aralığı filtresi
            if (parameters.MinAmount.HasValue)
            {
                query = query.Where(e => e.Amount >= parameters.MinAmount.Value);
            }

            if (parameters.MaxAmount.HasValue)
            {
                query = query.Where(e => e.Amount <= parameters.MaxAmount.Value);
            }

            return query;
        }

        private IQueryable<Expense> ApplySorting(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            if (string.IsNullOrEmpty(parameters.SortBy))
            {
                return query.OrderByDescending(e => e.ExpenseDate);
            }

            var isDescending = parameters.SortDirection?.ToLower() == "desc";

            return parameters.SortBy.ToLower() switch
            {
                "expensedate" => isDescending ? query.OrderByDescending(e => e.ExpenseDate) : query.OrderBy(e => e.ExpenseDate),
                "amount" => isDescending ? query.OrderByDescending(e => e.Amount) : query.OrderBy(e => e.Amount),
                "expensetype" => isDescending ? query.OrderByDescending(e => e.ExpenseType) : query.OrderBy(e => e.ExpenseType),
                "description" => isDescending ? query.OrderByDescending(e => e.Description) : query.OrderBy(e => e.Description),
                "creationdate" => isDescending ? query.OrderByDescending(e => e.CreationDate) : query.OrderBy(e => e.CreationDate),
                _ => query.OrderByDescending(e => e.ExpenseDate)
            };
        }
    }
}